/**
 * 基础JavaScript功能
 */

// 全局标签页管理器实例
let tabManager = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * 初始化应用
 */
function initializeApp() {
    // 初始化标签页管理器
    initializeTabManager();

    // 初始化侧边栏
    initializeSidebar();

    // 初始化侧边栏切换功能
    initializeSidebarToggle();

    // 初始化键盘快捷键
    initializeKeyboardShortcuts();

    // 初始化用户信息
    initializeUserInfo();

    // 设置当前页面的导航高亮
    highlightCurrentNav();
}

/**
 * 初始化侧边栏
 */
function initializeSidebar() {
    // 处理折叠菜单
    const collapseElements = document.querySelectorAll('[data-bs-toggle="collapse"]');
    collapseElements.forEach(element => {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            const targetSelector = this.getAttribute('data-bs-target');
            const target = document.querySelector(targetSelector);
            if (target) {
                // 检查是否已经有Collapse实例
                let collapse = bootstrap.Collapse.getInstance(target);
                if (!collapse) {
                    collapse = new bootstrap.Collapse(target, {
                        toggle: false
                    });
                }
                collapse.toggle();
            }
        });
    });
}

/**
 * 初始化侧边栏切换功能
 */
function initializeSidebarToggle() {
    const toggleBtn = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');

    if (toggleBtn && sidebar && mainContent) {
        // 从localStorage读取侧边栏状态
        const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        if (isCollapsed) {
            toggleSidebar(true);
        }

        toggleBtn.addEventListener('click', function() {
            toggleSidebar();
        });

        // 设置按钮提示
        toggleBtn.title = '收起/展开菜单 (Ctrl+B)';
    }
}

/**
 * 切换侧边栏显示/隐藏
 */
function toggleSidebar(forceCollapse = null) {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    const toggleBtn = document.getElementById('sidebarToggle');

    if (!sidebar || !mainContent || !toggleBtn) return;

    const isCurrentlyCollapsed = sidebar.classList.contains('collapsed');
    const shouldCollapse = forceCollapse !== null ? forceCollapse : !isCurrentlyCollapsed;

    if (shouldCollapse) {
        // 收起侧边栏
        sidebar.classList.add('collapsed');
        document.body.classList.add('sidebar-collapsed');
        toggleBtn.innerHTML = '<i class="fas fa-bars"></i>';
        toggleBtn.title = '展开菜单';

        // 保存状态到localStorage
        localStorage.setItem('sidebarCollapsed', 'true');
    } else {
        // 展开侧边栏
        sidebar.classList.remove('collapsed');
        document.body.classList.remove('sidebar-collapsed');
        toggleBtn.innerHTML = '<i class="fas fa-times"></i>';
        toggleBtn.title = '收起菜单';

        // 保存状态到localStorage
        localStorage.setItem('sidebarCollapsed', 'false');
    }
}

/**
 * 初始化键盘快捷键
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + B 或 Cmd + B 切换侧边栏
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            toggleSidebar();
        }
    });
}

/**
 * 初始化用户信息
 */
function initializeUserInfo() {
    // 可以在这里加载用户信息
    console.log('用户信息初始化完成');
}

/**
 * 高亮当前导航
 */
function highlightCurrentNav() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.startsWith(href) && href !== '/') {
            link.classList.add('active');
            
            // 展开父级折叠菜单
            const parentCollapse = link.closest('.collapse');
            if (parentCollapse) {
                parentCollapse.classList.add('show');
            }
        }
    });
}

/**
 * 登出功能
 */
async function logout() {
    try {
        const result = await request.post('/auth/logout');
        showMessage('登出成功', 'success');
        setTimeout(() => {
            window.location.href = '/auth/login';
        }, 1000);
    } catch (error) {
        showMessage('登出失败: ' + error.message, 'error');
    }
}

/**
 * 显示"敬请期待"消息
 */
function showComingSoon() {
    showMessage('该功能正在开发中，敬请期待！', 'info');
}

/**
 * 格式化日期
 */
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

/**
 * 格式化数字
 */
function formatNumber(number, decimals = 2) {
    if (number === null || number === undefined) return '-';
    return Number(number).toFixed(decimals);
}

/**
 * 防抖函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 显示加载状态
 */
function showLoading(element, text = '加载中...') {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    
    if (element) {
        element.innerHTML = `
            <div class="text-center py-4">
                <div class="loading-spinner mb-2"></div>
                <div class="text-muted">${text}</div>
            </div>
        `;
    }
}

/**
 * 隐藏加载状态
 */
function hideLoading(element) {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    
    if (element) {
        element.innerHTML = '';
    }
}

/**
 * 确认对话框
 */
function confirmDialog(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * 复制到剪贴板
 */
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showMessage('已复制到剪贴板', 'success');
    } catch (error) {
        showMessage('复制失败', 'error');
    }
}

/**
 * 下载文件
 */
function downloadFile(url, filename) {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/**
 * 获取URL参数
 */
function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

/**
 * 设置URL参数
 */
function setUrlParameter(name, value) {
    const url = new URL(window.location);
    url.searchParams.set(name, value);
    window.history.pushState({}, '', url);
}

/**
 * 初始化标签页管理器
 */
function initializeTabManager() {
    // 只在登录用户页面初始化标签页管理器
    if (document.getElementById('mainTabs')) {
        tabManager = new TabManager();
    }
}

/**
 * 标签页管理器类
 */
class TabManager {
    constructor() {
        this.tabs = new Map(); // 存储标签页信息
        this.activeTabId = 'home';
        this.maxTabs = 10; // 最大标签页数量
        this.tabCounter = 0; // 标签页计数器

        this.init();
    }

    init() {
        // 初始化首页标签
        this.tabs.set('home', {
            id: 'home',
            title: '首页',
            url: '/',
            icon: 'fas fa-home',
            closable: false,
            loaded: true
        });

        // 绑定事件监听器
        this.bindEvents();

        // 处理初始URL
        this.handleInitialUrl();

        // 如果首页内容需要加载，则加载首页内容
        this.loadHomeContentIfNeeded();
    }

    bindEvents() {
        // 监听侧边栏链接点击
        this.bindSidebarLinks();

        // 监听标签页关闭按钮
        this.bindTabCloseButtons();

        // 监听浏览器前进后退
        window.addEventListener('popstate', (e) => {
            this.handlePopState(e);
        });

        // 监听标签页切换
        document.addEventListener('shown.bs.tab', (e) => {
            const tabId = e.target.getAttribute('data-tab-id');
            if (tabId) {
                this.activeTabId = tabId;
                this.updateUrl(tabId);
            }
        });
    }

    bindSidebarLinks() {
        const sidebarLinks = document.querySelectorAll('.sidebar .nav-link[href]:not([href="#"]):not([onclick])');

        sidebarLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();

                const href = link.getAttribute('href');
                const title = link.textContent.trim();
                const icon = link.querySelector('i') ? link.querySelector('i').className : 'fas fa-file';

                // 生成标签页ID
                const tabId = this.generateTabId(href);

                // 创建或切换到标签页
                this.createOrSwitchTab(tabId, title, href, icon);
            });
        });

        // 绑定首页中的标签页链接
        const tabLinks = document.querySelectorAll('.tab-link[href]:not([href="#"])');
        tabLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();

                const href = link.getAttribute('href');
                // 从侧边栏找到对应的链接信息
                const sidebarLink = document.querySelector(`.sidebar .nav-link[href="${href}"]`);
                if (sidebarLink) {
                    const title = sidebarLink.textContent.trim();
                    const icon = sidebarLink.querySelector('i') ? sidebarLink.querySelector('i').className : 'fas fa-file';

                    // 生成标签页ID
                    const tabId = this.generateTabId(href);

                    // 创建或切换到标签页
                    this.createOrSwitchTab(tabId, title, href, icon);
                }
            });
        });
    }

    bindTabCloseButtons() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-close-btn')) {
                e.preventDefault();
                e.stopPropagation();

                const tabId = e.target.getAttribute('data-tab-id');
                this.closeTab(tabId);
            }
        });
    }

    generateTabId(url) {
        // 从URL生成唯一的标签页ID
        const path = url.replace(/^\//, '').replace(/\//g, '-') || 'home';
        return path;
    }

    createOrSwitchTab(tabId, title, url, icon) {
        if (this.tabs.has(tabId)) {
            // 标签页已存在，直接切换
            this.switchTab(tabId);
        } else {
            // 检查标签页数量限制
            if (this.tabs.size >= this.maxTabs) {
                showMessage('标签页数量已达上限，请关闭一些标签页后再试', 'warning');
                return;
            }

            // 创建新标签页
            this.createTab(tabId, title, url, icon);
        }
    }

    createTab(tabId, title, url, icon) {
        // 添加到标签页映射
        this.tabs.set(tabId, {
            id: tabId,
            title: title,
            url: url,
            icon: icon,
            closable: true,
            loaded: false
        });

        // 创建标签页导航
        this.createTabNav(tabId, title, icon);

        // 创建标签页内容
        this.createTabContent(tabId);

        // 切换到新标签页
        this.switchTab(tabId);

        // 加载内容
        this.loadTabContent(tabId, url);
    }

    createTabNav(tabId, title, icon) {
        const tabsContainer = document.getElementById('mainTabs');
        const tabNav = document.createElement('li');
        tabNav.className = 'nav-item';
        tabNav.setAttribute('role', 'presentation');

        const isClosable = this.tabs.get(tabId).closable;
        const closeButton = isClosable ?
            `<button type="button" class="btn-close btn-close-sm ms-2 tab-close-btn" data-tab-id="${tabId}" aria-label="关闭"></button>` : '';

        tabNav.innerHTML = `
            <button class="nav-link d-flex align-items-center" id="tab-${tabId}-tab" data-bs-toggle="tab"
                    data-bs-target="#tab-${tabId}" type="button" role="tab" aria-controls="tab-${tabId}"
                    aria-selected="false" data-tab-id="${tabId}">
                <i class="${icon} me-1"></i>
                <span class="tab-title">${title}</span>
                ${closeButton}
            </button>
        `;

        tabsContainer.appendChild(tabNav);
    }

    createTabContent(tabId) {
        const contentContainer = document.getElementById('mainTabContent');
        const tabContent = document.createElement('div');
        tabContent.className = 'tab-pane fade px-md-4 py-3';
        tabContent.id = `tab-${tabId}`;
        tabContent.setAttribute('role', 'tabpanel');
        tabContent.setAttribute('aria-labelledby', `tab-${tabId}-tab`);

        // 添加加载占位符
        tabContent.innerHTML = `
            <div class="loading-placeholder text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">正在加载内容...</p>
            </div>
        `;

        contentContainer.appendChild(tabContent);
    }

    switchTab(tabId) {
        if (!this.tabs.has(tabId)) {
            console.error(`Tab ${tabId} not found`);
            return;
        }

        // 使用 Bootstrap 的标签页切换
        const tabButton = document.querySelector(`#tab-${tabId}-tab`);
        if (tabButton) {
            const tab = new bootstrap.Tab(tabButton);
            tab.show();
        }

        this.activeTabId = tabId;
        this.updateUrl(tabId);
    }

    closeTab(tabId) {
        if (!this.tabs.has(tabId)) {
            return;
        }

        const tab = this.tabs.get(tabId);
        if (!tab.closable) {
            showMessage('首页标签页不能关闭', 'warning');
            return;
        }

        // 如果关闭的是当前活动标签页，需要切换到其他标签页
        if (this.activeTabId === tabId) {
            // 找到前一个标签页
            const tabIds = Array.from(this.tabs.keys());
            const currentIndex = tabIds.indexOf(tabId);
            const nextTabId = currentIndex > 0 ? tabIds[currentIndex - 1] : tabIds[currentIndex + 1] || 'home';

            this.switchTab(nextTabId);
        }

        // 移除DOM元素
        const tabNav = document.querySelector(`#tab-${tabId}-tab`).closest('.nav-item');
        const tabContent = document.querySelector(`#tab-${tabId}`);

        if (tabNav) tabNav.remove();
        if (tabContent) tabContent.remove();

        // 从映射中移除
        this.tabs.delete(tabId);
    }

    async loadTabContent(tabId, url) {
        const tab = this.tabs.get(tabId);
        if (!tab || tab.loaded) {
            return;
        }

        const tabContent = document.querySelector(`#tab-${tabId}`);
        if (!tabContent) {
            return;
        }

        try {
            // 发送AJAX请求获取页面内容
            const response = await fetch(url, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const html = await response.text();

            // 解析HTML并提取主要内容
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            // 查找页面主要内容（假设在 block content 中）
            let content = doc.querySelector('main') || doc.querySelector('.container') || doc.body;

            // 如果是完整的HTML页面，提取body内容
            if (content.tagName === 'BODY') {
                // 移除导航栏和侧边栏等元素
                const nav = content.querySelector('nav');
                const sidebar = content.querySelector('.sidebar');
                if (nav) nav.remove();
                if (sidebar) sidebar.remove();
            }

            // 更新标签页内容
            tabContent.innerHTML = content.innerHTML;

            // 标记为已加载
            tab.loaded = true;

            // 初始化页面特定的JavaScript
            this.initializeTabScripts(tabId, url);

        } catch (error) {
            console.error('Failed to load tab content:', error);
            tabContent.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <h4 class="alert-heading">加载失败</h4>
                    <p>无法加载页面内容: ${error.message}</p>
                    <hr>
                    <button class="btn btn-outline-danger" onclick="tabManager.reloadTab('${tabId}')">
                        <i class="fas fa-redo me-1"></i>重新加载
                    </button>
                </div>
            `;
        }
    }

    initializeTabScripts(tabId, url) {
        // 根据URL初始化对应的JavaScript功能
        const path = new URL(url, window.location.origin).pathname;

        if (path.includes('/airtightness/comparison')) {
            // 初始化气密性对比页面
            if (window.AirtightnessComparisonManager) {
                setTimeout(() => {
                    window.airtightnessManager = new AirtightnessComparisonManager();
                }, 100);
            }
        } else if (path.includes('/airtightness/images')) {
            // 初始化气密性图片页面
            if (window.AirtightnessImageManager) {
                setTimeout(() => {
                    window.imageManager = new AirtightnessImageManager();
                }, 100);
            }
        }
        // 可以根据需要添加更多页面的初始化逻辑
    }

    reloadTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab) return;

        tab.loaded = false;
        this.loadTabContent(tabId, tab.url);
    }

    updateUrl(tabId) {
        if (tabId === 'home') {
            // 首页不需要参数
            const url = new URL(window.location);
            url.searchParams.delete('tab');
            window.history.replaceState({}, '', url.pathname + url.search);
        } else {
            // 其他标签页添加tab参数
            setUrlParameter('tab', tabId);
        }
    }

    handleInitialUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        const tabParam = urlParams.get('tab');

        if (tabParam && tabParam !== 'home') {
            // 根据URL参数恢复标签页
            this.restoreTabFromUrl(tabParam);
        }
    }

    restoreTabFromUrl(tabId) {
        // 这里需要根据tabId推断对应的URL和标题
        // 简化实现：直接从当前页面URL推断
        const currentPath = window.location.pathname;

        if (currentPath !== '/') {
            // 从侧边栏找到对应的链接信息
            const sidebarLink = document.querySelector(`.sidebar .nav-link[href="${currentPath}"]`);
            if (sidebarLink) {
                const title = sidebarLink.textContent.trim();
                const icon = sidebarLink.querySelector('i') ? sidebarLink.querySelector('i').className : 'fas fa-file';

                this.createTab(tabId, title, currentPath, icon);
            }
        }
    }

    handlePopState(e) {
        const urlParams = new URLSearchParams(window.location.search);
        const tabParam = urlParams.get('tab') || 'home';

        if (this.tabs.has(tabParam)) {
            this.switchTab(tabParam);
        }
    }

    loadHomeContentIfNeeded() {
        // 检查是否需要加载首页内容
        const homeLoading = document.getElementById('home-loading');
        if (homeLoading) {
            // 需要通过AJAX加载首页内容
            this.loadTabContent('home', '/');
        }
    }
}
