{% extends "base.html" %}

{% block title %}首页 - NVH数据管理系统{% endblock %}

{% block content %}
<!-- 标签页中不需要重复显示标题，因为标签页本身就有标题 -->
<!-- 如果是直接访问（非标签页模式），则显示标题 -->
<script>
// 检查是否在标签页中
if (!document.getElementById('mainTabs')) {
    document.write('<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"><h1 class="h2">欢迎使用NVH数据管理系统</h1></div>');
}
</script>

<div class="row">
    <div class="col-12">
        <div class="welcome-section text-center py-5">
            <h2 class="mb-4">欢迎使用NVH数据管理系统</h2>
            <p class="lead text-muted mb-4">请从左侧菜单选择功能模块进行操作</p>

            <div class="row mt-5">
                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-wave-square fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">模态数据查询</h5>
                            <p class="card-text">查询和分析车辆及零部件的模态测试数据</p>
                            <a href="{{ url_for('modal.search_page') }}" class="btn btn-primary tab-link">进入查询</a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-tachometer-alt fa-3x text-info mb-3"></i>
                            <h5 class="card-title">气密性测试</h5>
                            <p class="card-text">车辆气密性泄漏量对比和测试图片查看</p>
                            <a href="{{ url_for('airtightness.comparison_page') }}" class="btn btn-info tab-link">进入查询</a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-volume-up fa-3x text-success mb-3"></i>
                            <h5 class="card-title">吸隔声模块</h5>
                            <p class="card-text">区域隔声量ATF对比分析和测试图片查看</p>
                            <a href="{{ url_for('sound_insulation.area_comparison_page') }}" class="btn btn-success tab-link">进入查询</a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-music fa-3x text-warning mb-3"></i>
                            <h5 class="card-title">声品质数据</h5>
                            <p class="card-text">声品质测试数据管理和分析</p>
                            <button class="btn btn-warning" onclick="showComingSoon()">敬请期待</button>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-area fa-3x text-secondary mb-3"></i>
                            <h5 class="card-title">振动数据</h5>
                            <p class="card-text">振动测试数据管理和分析</p>
                            <button class="btn btn-secondary" onclick="showComingSoon()">敬请期待</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
